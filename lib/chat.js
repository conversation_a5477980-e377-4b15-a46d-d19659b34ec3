const mongoose = require('mongoose');
const cmp = require('semver-compare');
const cryptoRandomString = require('crypto-random-string');
const { DateTime } = require('luxon');
const moment = require('moment');
const personalityLib = require('./personality');
const promptsLib = require('./prompts');
const locationLib = require('./location');
const premiumLib = require('./premium');
const constants = require('./constants');
const { formatInterests } = require('./interest');
const notificationLib = require('./notification');
const User = require('../models/user');
const Chat = require('../models/chat');
const Message = require('../models/message');
const Unmatched = require('../models/unmatched');
const BoostMetric = require('../models/boost-metric');
const { religionsBatch2 } = require('../lib/moreAboutUser');
const { isLocal } = require('./location');
const { applicationError } = require('./http-errors');
const admin = require('../config/firebase-admin');
const { sendSocketEvent } = require('./socket');
const projections = require('../lib/projections');
const { sendMessageNotifications, formatMessage} = require('./message');
const languageLib = require('../lib/languages');
const { getChatAnalysisResultForYourTurn } = require('./openai')
const { transcribeAudio } = require('../lib/deepgram');
const ChatAnalysisYourTurn = require('../models/chat-analysis-your-turn')
const ChatMetric = require('../models/chat-metric')
const { translate } = require('./translate');
const { includeIfValid } = require('./basic');

let BOO_SUPPORT_ID = 'MOCK_BOO_SUPPORT_ID';
let BOO_TRANSLATION_ID = 'MOCK_BOO_TRANSLATION_ID';
let BOO_BOT_ID = 'MOCK_BOO_BOT_ID';
const AUTOMATED_SUPPORT_REPLY = `We apologize for the long wait time on responding to your message. We are discontinuing in-app chat support for the time being, please email <NAME_EMAIL> going forward.`;

if (process.env.NODE_ENV == 'prod') {
  BOO_SUPPORT_ID = '7f6JuVty7Ibv4uftWN0VCSTLkC92';
  BOO_TRANSLATION_ID = 'wKIxfM9H1MR26OSlWTANbgdRcB53';
  BOO_BOT_ID = '7nrdloxknigtiZUBvBnLb75sE7t2';
}
if (process.env.NODE_ENV == 'beta') {
  BOO_SUPPORT_ID = 'd8gO6Fkju0hTMGBAfVijrdwXniw1';
  BOO_TRANSLATION_ID = 'CcerOSRibchkhHbSQpI39vbM8Pl1';
  BOO_BOT_ID = 'K5NJdluHtHeDg9kY6ECdxHX59o03';
}
if (process.env.NODE_ENV == 'dev') {
  BOO_SUPPORT_ID = '6l6EkhCTZCS3GGqgF9GRhZ8o7lh1';
}


function formatProfile(user, requestingUser, params = {}) {
  if (!user) {
    return null;
  }

  let tags
  if(params.includeTags || (params.requestSource == 'chats' && requestingUser.versionAtLeast('1.13.102'))){
    const { getProfileTags } = require('./profiles-v3')
    tags = getProfileTags(requestingUser, user);
  }

  if (user._id == BOO_SUPPORT_ID || user._id == BOO_TRANSLATION_ID || user._id == BOO_BOT_ID) {
    return {
      _id: user._id,
      firstName: user.firstName,
      pictures: user.pictures,
      description: user.description,
      education: user.education,
      work: user.work,
      crown: user.crown,
      handle: user.handle,
      interests: [],
      verified: true,
      verificationStatus: 'verified',
    };
  }

  const formattedPreferences = {
    showToVerifiedOnly: user.preferences.showToVerifiedOnly,
    purpose: [],
  };
  const userPreferences = premiumLib.getPreferences(user);
  // backwards compatibility - old versions cannot handle empty purpose array
  if (requestingUser && (!requestingUser.appVersion || cmp(requestingUser.appVersion, '1.10.17') < 0)) {
    if (userPreferences.dating.length > 0
      || userPreferences.purpose && userPreferences.purpose.includes('dating')) {
      formattedPreferences.purpose.push('dating');
    }
    if (userPreferences.friends.length > 0
      || userPreferences.purpose && userPreferences.purpose.includes('friends')) {
      formattedPreferences.purpose.push('friends');
    }
    if (formattedPreferences.purpose.length == 0) {
      formattedPreferences.purpose = ['dating', 'friends'];
    }
  } else if (!requestingUser || user._id == requestingUser._id) {
    if (userPreferences.dating.length > 0) {
      formattedPreferences.purpose.push('dating');
    }
    if (userPreferences.friends.length > 0) {
      formattedPreferences.purpose.push('friends');
    }
  } else {
    const requesterPreferences = premiumLib.getPreferences(requestingUser);
    if (requesterPreferences.dating.includes(user.gender)
        && userPreferences.dating.includes(requestingUser.gender)) {
      formattedPreferences.purpose.push('dating');
    }
    if (requesterPreferences.friends.includes(user.gender)
        && userPreferences.friends.includes(requestingUser.gender)) {
      formattedPreferences.purpose.push('friends');
    }
  }

  let horoscope = null;
  if (user.horoscope && (!user.hideHoroscope || !requestingUser || !requestingUser.versionAtLeast('1.11.27'))) {
    horoscope = user.horoscope;
  }

  let { pictures } = user;
  if (pictures && pictures.length > 0) {
    if (requestingUser && !requestingUser.versionAtLeast('1.11.46')) {
      // filter out videos for old versions
      const videoExtensions = ['.mp4', '.webm', '.avi', '.flv', '.mkv', '.mpg', '.wmv', '.mov'];
      pictures = pictures.filter((fileName) => !videoExtensions.some((ext) => fileName.includes(ext)));
    } else {
      // replace videos with converted hls files
      /*
      pictures = pictures.map((key) => {
        if (user.convertedVideos && user.convertedVideos.includes(key)) {
          // key format: uid/base.mpg
          const base = key.split('/').pop().split('.')[0];
          return `${key}/${base}.m3u8`;
        }
        return key;
      });
      */
    }
  }

  // Ship APP-798: false
  if (pictures?.length && user.privatePictures?.length) {
    pictures = pictures.filter(picture => !user.privatePictures.includes(picture));
  }

  let profilePicture;
  if (pictures && pictures.length > 0) {
    profilePicture = pictures[0];
  }

  const sevenDayAgo = DateTime.utc().minus({ days: 7 }).toJSDate();
  const inactive = user.updatedAt < sevenDayAgo;

  let verificationStatus = 'unverified';
  if (user.verification) {
    if (user.verification.status == 'verified') {
      verificationStatus = 'verified';
    }
    if (user.verification.status == 'reverifying') {
      verificationStatus = 'reverifying';
    }
  }

  let nearby;
  if (
    params.nearby
    && requestingUser
    && user._id != requestingUser._id
    && isLocal(user, requestingUser)
  ) {
    nearby = true;
  }

  let locale; let
    admin;
  if (requestingUser) {
    locale = requestingUser.locale;
    admin = requestingUser.admin;
  }

  let location = locationLib.getFormattedLocationFromUser(user, requestingUser);
  if (user.hideLocation) {
    location = null;
  }

  let languages;
  if (user.languages && user.languages.length > 0) {
    languages = user.languages;
    if (requestingUser && !requestingUser.versionAtLeast('1.13.74')) {
      languages = languages.filter(x => !languageLib.languageCodesBatch2.includes(x));
    }
    if (requestingUser && !requestingUser.versionAtLeast('1.13.75')) {
      languages = languages.filter(x => !languageLib.languageCodesBatch3.includes(x) && !languageLib.languageCodesBatch4.includes(x));
    }
  }

  let { prompts } = user;
  if (!requestingUser || !requestingUser.versionAtLeast('1.11.42')) {
    prompts = promptsLib.getFormattedPrompts(prompts, locale);
  }
  prompts = promptsLib.filterPrompts(prompts, requestingUser);

  let moreAboutUser;
  if (requestingUser && requestingUser.versionAtLeast('1.11.44')) {
    moreAboutUser = { ... user.moreAboutUser || {} };
    if (!moreAboutUser) {
      moreAboutUser = {};
    }
    if (!requestingUser.versionAtLeast('1.13.74') && religionsBatch2.includes(moreAboutUser.religion)) {
      moreAboutUser.religion = undefined;
    }

    moreAboutUser = Object.fromEntries(
      Object.entries(moreAboutUser).filter(([_, v]) => v !== undefined)
    );

    if (typeof moreAboutUser == 'object' && Object.keys(moreAboutUser).length === 0) {
      moreAboutUser = null;
    }
  }

  let hidden;
  if (requestingUser && requestingUser.versionAtLeast('1.12.8')) {
    hidden = user.hidden;
  }

  let numFollowers = user.metrics?.numFollowers || 0;
  if (requestingUser?.versionAtLeast('1.13.7')) {
      numFollowers = null;
  }

  let ethnicities;
  if (requestingUser && requestingUser.versionAtLeast('1.13.24')) {
    ethnicities = user.ethnicities;
  }

  let interestPoints;
  if (requestingUser && requestingUser.versionAtLeast('1.13.35')) {
    interestPoints = user.interestPoints;
  }

  let age = user.age;
  if (isNaN(age)) {
    age = null;
  }
  let karma = Math.round(user.karma);
  let awards = user.awards;
  if (requestingUser?.versionAtLeast('1.13.52')) {
    if (!params.isUniverse) {
      karma = null
      awards = null
    }
    age = (user.hideMyAge) ? null : age;
  }

  let premium;
  if (requestingUser?.versionAtLeast('1.13.76') && user.showMyInfinityStatus && premiumLib.isPremium(user)) {
    premium = true;
  }

  let sexuality;
  if (requestingUser?.versionAtLeast('1.13.59')) {
    const { sexuality: requesterSexuality, gender, preferences } = requestingUser;
    const isLGBTQ = requesterSexuality && !['heterosexual', 'asexual', 'other'].includes(requesterSexuality);
    const prefersLGBTQ = !requesterSexuality && (preferences.dating.includes(gender) || preferences.dating.includes('non-binary'));

    switch (user.sexualityVisibility) {
    case 'all':
      sexuality = user.sexuality;
      break;
    case 'sameSexuality':
      sexuality = user.sexuality === requesterSexuality ? user.sexuality : undefined;
      break;
    case 'lgbtq':
      sexuality = isLGBTQ || prefersLGBTQ ? user.sexuality : undefined;
      break;
    default:
      // invisible
      sexuality = undefined;
    }
  }

  // APP-895-V2
  const onlySuperLikeVisible =
    requestingUser?.isConfigTrue?.('app_895_v2') &&
    requestingUser?.gender === 'male' &&
    requestingUser?.preferences?.dating?.length > 0 &&
    !(requestingUser?.preferences?.friends?.length > 0) &&
    user.gender === 'female' &&
    tags?.includes?.('Top Soul') &&
    getLikesReceivedInLast24Hours(user) >= 5;

  if (params.fromDailyProfiles || params.fromTopPickProfiles) {
    user.stories = []
  }

  const formatted = {
    _id: user._id,
    firstName: user.firstName,
    pictures,
    profilePicture,
    personality: personalityLib.getPersonality(user, locale),
    gender: user.gender ? user.gender : null,
    age,
    // height: user.height,
    ethnicities: ethnicities ? ethnicities : undefined,
    description: user.description,
    audioDescription: user.audioDescription,
    audioDescriptionWaveform: user.audioDescriptionWaveform,
    audioDescriptionDuration: user.audioDescriptionDuration,
    education: user.education,
    work: user.work,
    enneagram: user.enneagram,
    moreAboutUser,
    prompts,
    crown: premiumLib.isCrown(user),
    handle: (user.searchable || admin) ? user.handle : undefined,
    location,
    teleport: !!user.teleportLocation,
    preferences: formattedPreferences,
    hideQuestions: admin ? false : inactive ? true : user.hideQuestions,
    hideComments: admin ? false : inactive ? true : user.hideComments,
    horoscope,
    interests: formatInterests(user, locale),
    interestNames: user.interestNames,
    karma,
    numFollowers,
    verified: !!(user.verification && user.verification.status == 'verified'),
    verificationStatus,
    awards,
    nearby,
    languages,
    timezone: (!location || user.teleportLocation) ? undefined : user.timezone,
    spotify: user.spotify,
    hidden,
    stories: user.stories,
    interestPoints,
    relationshipStatus: user.relationshipStatus,
    datingSubPreferences: user.datingSubPreferences,
    relationshipType: user.relationshipType,
    sexuality,
    tags,
    premium,
    ...includeIfValid(onlySuperLikeVisible, 'onlySuperLikeVisible'),
  };

  return formatted;
}

function formatChat(chat, receivingUser) {
  // chat must be a regular Javascript object, not a mongoose document.
  // chat.users and chat.lastMessage must be populated before calling this function.
  const partner = chat.users.find((x) => x._id != receivingUser._id);
  let includeTags = false;
  chat.users = chat.users
    .map((x) => {
      return formatProfile(x, receivingUser, { includeTags: includeTags, requestSource: 'chats' } )
    }).filter((x) => x._id != receivingUser._id);
  chat.user = chat.users[0];
  if (!chat.user) {
    chat.user = formatProfile(receivingUser, receivingUser, { includeTags: includeTags, requestSource: 'chats' });
  }

  if (chat.lastMessage) {
    chat.lastMessage = formatMessage(chat.lastMessage, false, receivingUser, false, true);
  }

  let viewLastSeenExpiration;
  let numUnreadMessages = 0;
  let matchIndicator;
  const readReceipt = chat.readReceipts[receivingUser._id];
  if (readReceipt) {
    numUnreadMessages = readReceipt.numUnreadMessages;
    matchIndicator = premiumLib.isPremium(receivingUser) ? readReceipt.matchIndicator : undefined;
    if (readReceipt.viewLastSeenExpiration > new Date()) {
      chat.user.lastSeen = partner.metrics.lastSeen;
      viewLastSeenExpiration = readReceipt.viewLastSeenExpiration;
    }
  }

  let partnerNumUnreadMessages;
  if (!chat.groupChat && premiumLib.isPremiumV1OrGodMode(receivingUser) && !(premiumLib.isPremiumV1OrGodMode(partner) && partner.hideReadReceipts)) {
    const readReceipt = chat.readReceipts[partner._id];
    if (readReceipt) {
      partnerNumUnreadMessages = readReceipt.numUnreadMessages;
    }
  }

  let numMessages = 0;
  if (chat.numMessages) {
    numMessages = chat.numMessages;
  }

  let pinned;
  if (chat.pinned && chat.pinned.includes(receivingUser._id)) {
    pinned = true;
  }

  let dndPost,dndMessage;

  if (receivingUser.appVersion && (cmp(receivingUser.appVersion, '1.11.75') >= 0)) {
    if (!chat.groupChat) {
      dndPost = (chat.dndPostFrom) ? chat.dndPostFrom.filter(user => receivingUser._id != user).length > 0 : false;
      dndMessage = (chat.dndMessageFrom) ? chat.dndMessageFrom.filter(user => receivingUser._id != user).length > 0 : false;
    }
  }

  let noreply;
  const isBooBot = chat?.user?._id === BOO_BOT_ID;
  if (!receivingUser?.versionAtLeast('1.13.67')) {
    noreply = isBooBot ? true : undefined;
  } else if (chat.automatedChat) {
    const { supportAdded, stage } = chat.automatedChatState;
    noreply = (supportAdded || stage === 'message_for_support') ? undefined : true;
  } else {
    noreply = isBooBot ? true : undefined;
  }

  //per user state
  let yourTurn = false
  if(chat.perUserState?.length && !chat.bannedUsers?.length){
    yourTurn = ['yourTurn', 'uncategorized'].includes(chat.perUserState.find(state => state.userId?.toString() === receivingUser._id)?.yourTurnState || '');
  }

  return {
    _id: chat._id,
    createdAt: chat.createdAt,
    users: chat.users,
    user: chat.user,
    lastMessage: chat.lastMessage,
    lastMessageTime: chat.lastMessageTime,
    lastMessageReaction: chat.lastMessageReaction,
    numMessages,
    numUnreadMessages,
    partnerNumUnreadMessages,
    pendingUser: chat.pendingUser,
    instantMatch: chat.instantMatch,
    expirationDate: getChatExpirationDate(chat),
    groupChat: chat.groupChat,
    groupChatName: chat.groupChatName,
    muted: chat.muted ? chat.muted.includes(receivingUser._id) : false,
    pinned,
    viewLastSeenExpiration,
    dndMessage,
    dndPost,
    initiatedBySuperLike: chat.initiatedBySuperLike,
    matchIndicator,
    noreply,
    automatedChat: receivingUser?.versionAtLeast('1.13.67') && chat.automatedChat ? true : undefined,
    supportAdded: receivingUser?.versionAtLeast('1.13.67') && chat.automatedChat && chat.automatedChatState?.supportAdded ? true : undefined,
    yourTurn : yourTurn,
  };
}

function incrementUnreadMessages(chat, uid) {
  let readReceipt = chat.readReceipts.get(uid);
  if (readReceipt) {
    readReceipt.numUnreadMessages += 1;
  } else {
    readReceipt = {
      numUnreadMessages: 1,
    };
  }
  chat.readReceipts.set(uid, readReceipt);

  // unhide chat for everyone
  chat.hidden = [];
}

function decrementUnreadMessages(chat, uid) {
  let readReceipt = chat.readReceipts.get(uid);
  if (readReceipt && readReceipt.numUnreadMessages > 0) {
    readReceipt.numUnreadMessages -= 1;
  } else {
    readReceipt = {
      numUnreadMessages: 0,
    };
  }
  chat.readReceipts.set(uid, readReceipt);

  // unhide chat for everyone
  chat.hidden = [];
}

async function clearUnreadMessages(chat, uid) {
  await notificationLib.resetNotificationBadgeCount(uid);
  if (!chat) {
    return;
  }
  let readReceipt = chat.readReceipts.get(uid);
  if (readReceipt) {
    readReceipt.numUnreadMessages = 0;
  } else {
    readReceipt = {
      numUnreadMessages: 0,
    };
  }
  chat.readReceipts.set(uid, readReceipt);

  try {
    await chat.save();
  } catch (err) {
    console.log(err);
  }
}

function isChatVisible(chat, user) {
  if (chat.groupChat) {
    if (user.shadowBanned) {
      return false;
    }
    if (!user.supportsGroupChat()) {
      return false;
    }
    return true;
  }

  if (
    chat.users.length <= 1
    || chat.users[0].banned
    || chat.users[1].banned
    || chat.users[0]._id == chat.users[1]._id
  ) {
    return false;
  }

  const otherUser = chat.users.find((u) => u._id != user._id);
  if (otherUser.shadowBanned) {
    return false;
  }

  if (chat.pendingUser
    && chat.pendingUser == user._id
    && otherUser.metrics
    && otherUser.metrics.numPendingReports > 0) {
    return false;
  }

  if (constants.hideUnverifiedUsers()
    && chat.pendingUser
    && chat.pendingUser == user._id
    && user.hideUnverifiedUsers !== false
    && !['verified', 'reverifying'].includes(otherUser.verification?.status)) {
    return false;
  }

  if (user._id != BOO_SUPPORT_ID && chat.users.some(u => u._id == BOO_SUPPORT_ID)) {
    return false;
  }

  return true;
}

function filterChats(chats, user) {
  const filteredChats = chats.filter((chat) => isChatVisible(chat, user));

  return filteredChats;
}

function formatChats(chats, user) {
  return chats.map((chatRv) => formatChat(chatRv, user));
}

async function getChats(user, query, beforeDate, pageSize, disableHiddenQuery) {
  // exclude hidden chats
  if(!disableHiddenQuery){
    query.hidden = { $ne: user._id };
  }

  // exclude deleted chats
  query.deletedAt = null;

  // exclude chats where the other user is banned
  query.bannedUsers = { $in: [ null, [], user._id ] };

  if (!pageSize) {
    pageSize = constants.getPageSize();
  }

  let chats = [];
  while (!chats.length) {
    if (beforeDate) {
      if (!query.lastMessageTime) {
        query.lastMessageTime = {};
      }
      query.lastMessageTime.$lt = new Date(beforeDate);
    }

    const unfilteredChats = await Chat
      .find(query)
      .sort('-lastMessageTime')
      .limit(pageSize)
      .populate('users', projections.fullProfileFieldsStr)
      .populate('lastMessage')
      .lean();

    // if no more chats, then return
    if (!unfilteredChats.length) {
      break;
    }

    // filter the chats and update beforeDate in case we need to repeat
    chats = filterChats(unfilteredChats, user);
    beforeDate = unfilteredChats[unfilteredChats.length - 1].lastMessageTime;
    if (!beforeDate) {
      break;
    }
  }

  return formatChats(chats, user);
}

async function getPendingChats(user, before) {
  let chats = [];
  if (!before) {
    const query = {
      users: user._id,
      pendingUser: user._id,
      initiatedBySuperLike: true,
      pending: true,
      rejected: { $ne: true },
    };
    chats = await getChats(user, query, before);
  }

  const query = {
    users: user._id,
    pendingUser: user._id,
    initiatedBySuperLike: null,
    rejected: { $ne: true },
  };
  chats = chats.concat(await getChats(user, query, before));
  return chats;
}

async function getApprovedChats(user, beforeDate, beforeChat, pinned) {
  let chats = [];
  let lookupPinned = true;

  if (beforeChat && !beforeChat.pinned.includes(user._id)) {
    lookupPinned = false;
  }
  if (beforeDate && !beforeChat) {
    lookupPinned = false;
  }
  if (pinned == true || pinned == 'true') {
    lookupPinned = true;
  }

  if (lookupPinned) {
    const query = {
      users: user._id,
      pendingUser: null,
      pinned: user._id,
    };
    chats = chats.concat(await getChats(user, query, beforeDate));
    if (chats.length >= constants.getPageSize()) {
      return chats;
    }
    beforeDate = undefined;
  }

  const query = {
    users: user._id,
    pendingUser: null,
    pinned: { $ne: user._id },
  };
  chats = chats.concat(await getChats(user, query, beforeDate, constants.getPageSize() - chats.length));
  return chats;
}

async function getMessagedChats(user, beforeDate, beforeChat, pinned) {
  let chats = [];
  let lookupPinned = true;

  if (beforeChat && !beforeChat.pinned.includes(user._id)) {
    lookupPinned = false;
  }
  if (beforeDate && !beforeChat) {
    lookupPinned = false;
  }
  if (pinned == true || pinned == 'true') {
    lookupPinned = true;
  }

  if (lookupPinned) {
    const query = {
      users: user._id,
      pendingUser: null,
      messaged: true,
      pinned: user._id,
    };
    chats = chats.concat(await getChats(user, query, beforeDate));
    if (chats.length >= constants.getPageSize()) {
      return chats;
    }
    beforeDate = undefined;
  }

  let pageSize = constants.getPageSize();
  if (user.versionAtLeast('1.13.70')) {
    pageSize = 50;
  }

  const query = {
    users: user._id,
    pendingUser: null,
    messaged: true,
    pinned: { $ne: user._id },
  };
  chats = chats.concat(await getChats(user, query, beforeDate, pageSize - chats.length));
  return chats;
}

async function getPinnedChats(user, beforeDate, pageSize) {
  let chats = [];
  if(pageSize < 1) {
    return chats
  }
  const query = {
    users: user._id,
    pendingUser: null,
    messaged: true,
    pinned: user._id,
  };
  chats = chats.concat(await getChats(user, query, beforeDate, pageSize - chats.length));
  return chats;
}

async function getMessagedChatsV2(user, beforeDate, pageSize) {
  let chats = [];
  if(pageSize < 1) {
    return chats
  }
  const query = {
    users: user._id,
    pendingUser: null,
    messaged: true,
    pinned: { $ne: user._id },
  };
  chats = chats.concat(await getChats(user, query, beforeDate, pageSize - chats.length));
  return chats;
}

async function getMessagedYourTurnChats(user, beforeDate, pageSize, disablePinnedFilter) {
  let chats = [];
  if(pageSize < 1) {
    return chats
  }
  const query = {
    perUserState : {
      $elemMatch: {
        userId: user._id,
        yourTurnState: { $in: ["uncategorized", "yourTurn"] },
      }
    },
    pendingUser: null,
    messaged: true,
    pinned: { $ne: user._id },
  };
  if(disablePinnedFilter) {
    delete query.pinned;
  }
  chats = chats.concat(await getChats(user, query, beforeDate, pageSize - chats.length, true));
  return chats;
}

async function getMessagedNotYourTurnChats(user, beforeDate, pageSize) {
  let chats = [];
  if(pageSize < 1) {
    return chats
  }
  const query = {
    perUserState : {
      $elemMatch: {
        userId: user._id,
        yourTurnState: { $in: [null, 'concluded', 'messageSent'] },
      }
    },
    pendingUser: null,
    messaged: true,
    pinned: { $ne: user._id },
  };
  chats = chats.concat(await getChats(user, query, beforeDate, pageSize - chats.length));
  return chats;
}

async function getMessagedUnreadChats(user, beforeDate, pageSize) {
  let chats = [];
  if(pageSize < 1) {
    return chats
  }
  const query = {
    perUserState : {
      $elemMatch: {
        userId: user._id,
        unread: true,
      }
    },
    pendingUser: null,
    messaged: true,
    pinned: { $ne: user._id },
  };
  chats = chats.concat(await getChats(user, query, beforeDate, pageSize - chats.length));
  return chats;
}

async function getMessagedNotUnreadChats(user, beforeDate, pageSize) {
  let chats = [];
  if(pageSize < 1) {
    return chats
  }
  const query = {
    perUserState : {
      $elemMatch: {
        userId: user._id,
        unread: false,
      }
    },
    pendingUser: null,
    messaged: true,
    pinned: { $ne: user._id },
  };
  chats = chats.concat(await getChats(user, query, beforeDate, pageSize - chats.length));
  return chats;
}

async function getNotMessagedChats(user, beforeDate) {
  let chats = [];
  const query = {
    users: user._id,
    pendingUser: null,
    messaged: null,
  };
  chats = chats.concat(await getChats(user, query, beforeDate, constants.getPageSize() - chats.length));
  return chats;
}

async function getContacts(user, sort, beforeId) {
  let beforeDate;
  if (beforeId) {
    const beforeChat = await Chat.findById(beforeId);
    if (beforeChat) {
      beforeDate = beforeChat.createdAt;
    }
  }

  const query = {
    users: user._id,
    pendingUser: null,
    groupChat: { $ne: true },
    deletedAt: null,
  };

  // default is latest
  let sortDirection = -1;
  let beforeOperator = '$lt';

  if (sort == 'earliest') {
    sortDirection = 1;
    beforeOperator = '$gt';
  }

  let chats = [];
  while (!chats.length) {
    if (beforeDate) {
      if (!query.createdAt) {
        query.createdAt = {};
      }
      query.createdAt[beforeOperator] = new Date(beforeDate);
    }

    // larger page size for ghost meter
    let pageSize = constants.getPageSize();
    if (user.versionAtLeast('1.13.77')) {
      pageSize = 100;
    }

    const unfilteredChats = await Chat
      .find(query)
      .sort({ createdAt: sortDirection })
      .limit(pageSize)
      .populate('users')
      .populate('lastMessage')
      .lean();

    // if no more chats, then return
    if (!unfilteredChats.length) {
      break;
    }

    // filter the chats and update beforeDate in case we need to repeat
    chats = filterChats(unfilteredChats, user);
    beforeDate = unfilteredChats[unfilteredChats.length - 1].createdAt;
    if (!beforeDate) {
      break;
    }
  }

  return formatChats(chats, user);
}

async function getSentChats(user, before) {
  const query = {
    createdBy: user._id,
    pending: true,
  };
  const chats = await getChats(user, query, before);
  return chats;
}

async function backfillNumMessages() {
  const chats = Chat.aggregate([
    {
      $match: {
        lastMessage: { $exists: true },
      },
    },
    {
      $lookup: {
        from: 'messages',
        localField: '_id',
        foreignField: 'chat',
        as: 'messages',
      },
    },
    {
      $addFields: {
        numMessages: { $size: '$messages' },
      },
    },
    {
      $project: {
        _id: 1,
        numMessages: 1,
      },
    },
  ]);

  let i = 0;
  let bulk = Chat.collection.initializeUnorderedBulkOp();
  for await (const chat of chats) {
    const filter = { _id: chat._id };
    const updates = {
      $set: {
        numMessages: chat.numMessages,
      },
    };
    bulk.find(filter).update(updates);
    i++;
    if (i % 100000 == 0) {
      const res = await bulk.execute();
      console.log(i, res);
      bulk = Chat.collection.initializeUnorderedBulkOp();
    }
  }
  const res = await bulk.execute();
  console.log(i, res);
}

async function backfillMessagedField() {
  await Chat.updateMany(
    { lastMessage: { $exists: true } },
    { messaged: true },
  );
}

function getChatExpirationDate(chat) {
  let expirationDate;
  if (chat.initiatedBySuperLike) {
    return;
  }
  if (chat.pendingUser) {
    expirationDate = DateTime.fromJSDate(chat.lastMessageTime).plus({ hours: constants.chatExpirationHours }).toJSDate();
  }
  return expirationDate;
}

function isChatExpired(chat) {
  const expirationDate = getChatExpirationDate(chat);
  return chat.pendingUser && Date.now() >= expirationDate;
}

async function checkForDeleteInstantMatch(chat) {
  if (!chat.instantMatch) {
    return;
  }

  for (const uid of chat.users) {
    await User.updateOne(
      {
        _id: uid,
      },
      {
        instantMatchPartner: null,
        instantMatchResetTime: DateTime.utc().plus({
          milliseconds: constants.instantMatchCooldownMs,
        }).toJSDate(),
      },
    );
  }
}

async function sendSupportUserReply(user, replyText) {
  const booSupportUser = await User.findById(BOO_SUPPORT_ID);

  if (!booSupportUser) {
    throw applicationError();
  }

  let newChat = false;

  let chat = await Chat.findDirectChat(user._id, booSupportUser._id, true);

  // create the chat if it does not exist yet
  if (!chat) {
    newChat = true;
    chat = new Chat({
      users: [user, booSupportUser],
      lastMessageTime: Date.now(),
      pendingUser: null,
    });
  }

  const now = Date.now();

  const reply = new Message({
    createdAt: now,
    chat: chat._id,
    text: replyText,
    sender: BOO_SUPPORT_ID,
  });
  const savedReply = await reply.save();

  // update the chat's last message
  chat.lastMessage = savedReply;
  chat.lastMessageTime = Date.now();
  chat.incrementNumMessages();
  incrementUnreadMessages(chat, user._id);

  const savedChat = await chat.save();

  if (newChat) {
    // send socket event to user
    const formattedChat = formatChat(
      savedChat.toObject({ flattenMaps: true }),
      user,
    );

    sendSocketEvent(user._id, 'approved chat', formattedChat);

    // send socket event to boo support
    const formattedChatForBoo = formatChat(
      savedChat.toObject({ flattenMaps: true }),
      booSupportUser,
    );

    sendSocketEvent(booSupportUser._id, 'approved chat', formattedChatForBoo);
  } else {
    sendSocketEvent(user._id, 'message', reply);
  }
  admin.sendNotification(
    user,
    'messages',
    'Boo',
    replyText,
    null,
    null,
    'love',
    'chat-message',
  );
}

async function sendAutomatedReply(user, replyText, triggeredMessage, additionalInfo) {
  // Triggered message will be true if they are from APP-417 logic. If true no firebase notification will be sent (only socket event) and additionalInfo will be added to the message if exists
  if (!user.versionAtLeast('1.13.64')) return;

  const botUser = await User.findById(BOO_BOT_ID);

  if (!botUser) {
    throw applicationError();
  }

  let newChat = false;
  const userIdHash = Chat.createUserIdHash([user._id, botUser._id]);
  let chat = await Chat.findOne({
    userIdHash,
    groupChat: { $ne: true },
    deletedAt: null,
    automatedChat: { $ne: true } })
    .populate('users')
    .populate('lastMessage');

  // create the chat if it does not exist yet
  if (!chat) {
    newChat = true;
    chat = new Chat({
      users: [user, botUser],
      lastMessageTime: Date.now(),
      pendingUser: null,
      messaged: true,
    });
  }

  const now = Date.now();

  const reply = new Message({
    createdAt: now,
    chat: chat._id,
    text: replyText,
    sender: BOO_BOT_ID,
    additionalInfo: triggeredMessage && additionalInfo ? JSON.stringify(additionalInfo) : undefined,
  });
  const savedReply = await reply.save();

  // update the chat's last message
  chat.lastMessage = savedReply;
  chat.lastMessageTime = Date.now();
  chat.incrementNumMessages();
  incrementUnreadMessages(chat, user._id);
  chat.perUserState = [
    {
      userId: user._id,
      unread: true,
    },
    {
      userId: BOO_BOT_ID,
      unread: false,
    }
  ]
  const savedChat = await chat.save();

  if (newChat) {
    // send socket event to user, no need to send socket event to bot
    const formattedChat = formatChat(
      savedChat.toObject({ flattenMaps: true }),
      user,
    );

    await sendSocketEvent(user._id, 'approved chat', formattedChat);
  }

  if (triggeredMessage) {
    const messageData = formatMessage(savedReply.toObject(), false, user);
    sendSocketEvent(user._id, 'message', messageData);
  } else {
    sendMessageNotifications(botUser, chat, savedReply, replyText);
  }
}

async function backfillUserIdHash() {
  const res = await Chat.updateMany(
    {
      userIdHash: null,
      users: { $size: 2 },
      groupChat: { $ne: true },
    },
    [
      {
        $set: {
          user0: { $arrayElemAt: ['$users', 0] },
          user1: { $arrayElemAt: ['$users', 1] },
        },
      },
      {
        $set: {
          userIdHash: {
            $concat: [
              {
                $cond: {
                  if: { $lt: ['$user0', '$user1'] },
                  then: '$user0',
                  else: '$user1',
                },
              },
              '-',
              {
                $cond: {
                  if: { $lt: ['$user0', '$user1'] },
                  then: '$user1',
                  else: '$user0',
                },
              },
            ],
          },
        },
      },
      {
        $unset: [
          'user0',
          'user1',
        ],
      },
    ],
  );
  console.log(res);
}

async function backfillPending() {
  let res = await Chat.updateMany(
    {
      pendingUser: { $exists: true },
    },
    [
      {
        $set: {
          user0: { $arrayElemAt: ['$users', 0] },
          user1: { $arrayElemAt: ['$users', 1] },
        },
      },
      {
        $set: {
          pending: { $ne: ['$pendingUser', null] },
          createdBy: {
            $switch: {
              branches: [
                { case: { $eq: ['$pendingUser', '$user0'] }, then: '$user1' },
                { case: { $eq: ['$pendingUser', '$user1'] }, then: '$user0' },
              ],
              default: '$createdBy',
            },
          },
        },
      },
      {
        $unset: [
          'user0',
          'user1',
        ],
      },
    ],
  );
  console.log(res);

  res = await Chat.updateMany(
    {
      pendingUser: { $exists: false },
    },
    [
      {
        $set: {
          pending: false,
        },
      },
    ],
  );
  console.log(res);
}

async function backfillUserPerStateInChats(userId) {
  const cursor = Chat.aggregate([
    {
      $match: {
        users: userId,
        messaged: true,
        deletedAt: null,
        $or: [
          { perUserState: { $eq: null } },
          {
            'perUserState.userId': { $ne: userId },
          },
        ],
      }
    },
    {
      $lookup: {
        from: 'messages',
        localField: 'lastMessage',
        foreignField: '_id',
        as: 'lastMessageData',
      },
    },
    { $unwind: { path: '$lastMessageData', preserveNullAndEmptyArrays: true } },
    { $project: { _id: 1, users: 1, groupChat: 1, perUserState: 1, readReceipts: 1, lastMessageData: 1 } }
  ]).cursor();

  let bulkOps = [];
  let batchSize = 100;

  for await (const chat of cursor) {
    if (chat.perUserState) continue
    chat.perUserState = [];
    const hasAnyBooChatIdOrGroupChat = chat.groupChat || [BOO_SUPPORT_ID, BOO_BOT_ID, BOO_TRANSLATION_ID].some(id => chat.users.includes(id));
    for (const chatUserId of chat.users) {
      const readReceipt = chat.readReceipts?.[chatUserId];
      const numUnreadMessages = readReceipt?.numUnreadMessages || 0;
      const lastMessageSender = chat.lastMessageData?.sender;
      const perUserEntry = {
        userId: chatUserId,
        unread: numUnreadMessages > 0,
        yourTurnState: lastMessageSender === chatUserId ? 'messageSent' : 'uncategorized',
      };
      if (hasAnyBooChatIdOrGroupChat) {
        perUserEntry.yourTurnState = undefined
      }
      chat.perUserState.push(perUserEntry);
    }
    bulkOps.push({
      updateOne: {
        filter: { _id: chat._id },
        update: { $set: { perUserState: chat.perUserState } },
      }
    });

    // Execute bulkWrite every 100 chats
    if (bulkOps.length >= batchSize) {
      await Chat.bulkWrite(bulkOps);
      bulkOps = []; // Clear batch
    }
  }
  // Execute remaining updates
  if (bulkOps.length > 0) {
    await Chat.bulkWrite(bulkOps);
    bulkOps = []
  }
  await cursor.close();
}

async function backfillUserNumYourTurnChats(userId) {
  let numYourTurnChats = await Chat.countDocuments({
    perUserState : {
      $elemMatch: {
        userId: userId,
        yourTurnState: { $in: ["uncategorized", "yourTurn"] },
      }
    },
    messaged: true,
    deletedAt: null,
    pendingUser: null,
    bannedUsers: { $in: [ null, []] },
  })
  return numYourTurnChats || 0
}

function encodePaginationToken(lastChat, chatType) {
  const tokenData = {
    pinned: lastChat.pinned ? lastChat.pinned : false,
    before: lastChat.lastMessageTime ? lastChat.lastMessageTime : null,
    chatType,
  }
  const tokenStringData = JSON.stringify(tokenData);
  return Buffer.from(tokenStringData).toString("base64url");
}

function decodePaginationToken(token) {
  if (!token || token === "null") return {};
  let { before, chatType, pinned } = JSON.parse(Buffer.from(token, "base64url").toString("utf-8"));
  return {
    beforeDate: before,
    chatType,
    pinned,
  }
}

async function getMessagedSortedChats(user, beforeDate, pinned, sort, chatType) {
    let chats = [];
    let pageSize = constants.getPageSize();
    if(pinned && sort != 'ghostMeter') { // pinned chats
      chats = chats.concat(await getPinnedChats(user, beforeDate, pageSize - chats.length))
      beforeDate = undefined
    }
    if (!(sort === 'ghostMeter' && user.shadowBanned) && chats.length < pageSize) {
      if (sort === 'unread') { // unread chats
        if(chatType){
          chats = chats.concat(await getMessagedUnreadChats(user, beforeDate, pageSize - chats.length));
          beforeDate = undefined
        }
        if (chats.length < pageSize) {
          chatType = false;
          chats = chats.concat(await getMessagedNotUnreadChats(user, beforeDate, pageSize - chats.length))
        }
      } else if (sort === 'yourTurn' && !user.shadowBanned) { // yourTurn chats
        if(chatType){
          chats = chats.concat(await getMessagedYourTurnChats(user, beforeDate, pageSize - chats.length));
          beforeDate = undefined
        }
        if (chats.length < pageSize) {
          chatType = false;
          chats = chats.concat(await getMessagedNotYourTurnChats(user, beforeDate, pageSize - chats.length))
        }
      } else if (sort === 'ghostMeter') { // ghostMeter chats
        chats = chats.concat(await getMessagedYourTurnChats(user, beforeDate, pageSize - chats.length, true));
      } else { // recent chats or shadowBanned users chat for yourTurn
        if (user.versionAtLeast('1.13.70')) pageSize = 50;
        chats = chats.concat(await getMessagedChatsV2(user, beforeDate, pageSize - chats.length))
      }
    }
    let paginationToken = null
    if(chats.length >= pageSize && chats.at(-1)){
      paginationToken = chats.at(-1) ? encodePaginationToken(chats.at(-1), chatType) : null;
    }else{
      paginationToken = null
    }
    return {
      chats,
      token: paginationToken,
    }
}

const fetchRecentMessagesForChats = async (chatIds) => {
  const messagesByChat = {};

  await Promise.all(
    chatIds.map(async (chatId) => {
      try {
        messagesByChat[chatId] = await Message.find({ chat: chatId, deletedAt: null, unsent: null }).select('text sender image audio transcribedAudio').sort({ createdAt: -1 }).limit(10).lean();
      } catch (error) {
        console.log(`Error fetching messages for chat ${chatId}:`, error);
        messagesByChat[chatId] = [];
      }
    }),
  );

  return messagesByChat;
};

const prepareRecentChat = async (messages) => {
  if (!messages.length) return { messages: '', imageUrls: [] }

  const lastSender = messages[0].sender;
  const reversedMessages = [...messages].reverse();
  const imageUrls = [];

  const formattedMessages = await Promise.all(
    reversedMessages.map(async (message) => {
      const senderLabel = message.sender === lastSender ? 'Friend' : 'Me';
      let text = message.text || '';

      if(message.audio && message.transcribedAudio){
        text = `[Transcription of voice message]: ${message.transcribedAudio}`;
      }else if(message.audio) {
        const transcription = await transcribeAudio(message.audio);
        if (transcription) {
          await Message.updateOne({ _id: message._id }, { transcribedAudio: transcription })
          text = `[Transcription of voice message]: ${transcription}`;
        }
      }

      if (message.image) {
        text = `[Check image at index ${imageUrls.length}]`;
        imageUrls.push(constants.IMAGE_DOMAIN + message.image);
      }

      return `${senderLabel}: ${text}`;
    }),
  );

  return { messages: formattedMessages.join('\n'), imageUrls };
};

function formatStoredPrompt(prompt, imageUrls) {
  return JSON.stringify({ prompt, imageUrls }, null, 2);
}

const processChatBatch = async (chats, userId) => {
  const chatIds = chats.map(chat => chat._id);
  const messagesByChat = await fetchRecentMessagesForChats(chatIds);
  return Promise.all(
    chats.map(async (chat) => {
      const chatId = chat._id;
      const chatMessages = messagesByChat[chatId] || [];
      const recentChat = await prepareRecentChat(chatMessages);
      const response = await getChatAnalysisResultForYourTurn(recentChat);
      let result = 'N/A'
      if(response.formattedOutputPrompts?.Status?.toLowerCase() == 'active'){
        result = 'active'
      }else if(response.formattedOutputPrompts?.Status?.toLowerCase() == 'concluded'){
        result = 'concluded'
      }
      let meetUp = response.formattedOutputPrompts?.['Met up']?.toLowerCase() == 'yes' ? true : false
      let contactExchange = response.formattedOutputPrompts?.['Contact details']?.toLowerCase() == 'yes' ? true : false
      let transactions = response.formattedOutputPrompts?.Transactions?.toLowerCase() == 'yes' ? true : false
      await ChatAnalysisYourTurn.create({
          userId: userId,
          chatId: chatId,
          prompt: formatStoredPrompt(response.prompt || '', recentChat.imageUrls || []),
          output:JSON.stringify({ output: response.output || '' }, null, 2),
          promptTokens: response.promptTokens,
          outputTokens: response.outputTokens,
          isError: response.errorMessage ? true : false,
          errorMessage: response.errorMessage || undefined,
          cost: response.cost,
          model: response.model,
          processingTime: response.processingTime || 0,
          inputText: JSON.stringify({ recentChat }, null, 2),
          result: result,
          meetUp: meetUp,
          transactions: transactions,
          contactExchange: contactExchange,
          formattedOutputPrompts: response.formattedOutputPrompts,
        });
      return {
        chatId: chatId,
        chat: chat,
        result: result,
        meetUp: meetUp,
        contactExchange: contactExchange,
        transactions: transactions,
        isError: response.errorMessage ? true : false,
        cost: response.cost,
        chatMessages: recentChat,
      };
    }),
  );
};

const finalizeYourTurnStateInChats = async (userId) => {
  {
    const chats = await Chat.find(
    {
      perUserState: {
        $elemMatch: {
          userId: userId,
          yourTurnState: 'uncategorized',
        },
      },
      messaged: true,
      deletedAt: null,
      pendingUser: null,
      groupChat: { $ne: true },
      bannedUsers: { $in: [ null, []] },
    })
    .select({
      _id: 1,
      users: 1,
      meetUp: 1,
      contactExchange: 1,
      transactions: 1,
      chatAnalyzed: 1
    }).lean();
    if(chats.length < 1) return

    const batchResults = await processChatBatch(chats, userId);
    let numConcluded = 0;
    let numMeetUp = 0;
    let numContactExchange = 0;
    let numMatchesAnalyzed = 0;

    for (const { chatId, chat, result, meetUp, contactExchange, transactions, isError } of batchResults) {
      if (isError) continue;
      let updateQuery = {};
      let incrementUpdateQuery = {}
      if(!chat.chatAnalyzed) {
        numMatchesAnalyzed++;
        incrementUpdateQuery['metrics.numMatchesAnalyzed'] = 1
        updateQuery.chatAnalyzed = true
      }
      if(!chat.meetUp) {
        if(meetUp) {
          numMeetUp++;
          incrementUpdateQuery['metrics.numMeetUpChats'] = 1
        }
        updateQuery.meetUp = meetUp
      }
      if(!chat.contactExchange) {
        if(contactExchange) {
          numContactExchange++;
          incrementUpdateQuery['metrics.numContactExchangeChats'] = 1
        }
        updateQuery.contactExchange = contactExchange
      }
      if(!chat.transactions) {
        updateQuery.transactions = transactions
      }
      if(result === 'active') {
        updateQuery['perUserState.$.yourTurnState'] = 'yourTurn'
      }else if(result === 'concluded') {
        numConcluded++;
        updateQuery['perUserState.$.yourTurnState'] = 'concluded'
      }
      if(Object.keys(updateQuery).length > 0) {
        await Chat.updateOne(
          { _id: chatId, 'perUserState.userId': userId },
          { $set: updateQuery }
        );
      }
      if(Object.keys(incrementUpdateQuery).length > 0 && chat.users.length == 2){
        await User.updateMany(
          { _id: { $in: chat.users } },
          { $inc: incrementUpdateQuery }
        );
      }
    }

    if (numConcluded > 0) {
      await sendSocketUpdateForNumYourTurnChats(userId);
      await User.incrementMetric(userId, 'numYourTurnChats', -1 * numConcluded)
    }

    if(numMeetUp > 0 || numContactExchange > 0 || numMatchesAnalyzed > 0){
      await ChatMetric.incrementDailyMetrics(numMeetUp, numContactExchange, numMatchesAnalyzed);
    }
  }
}

const sendSocketUpdateForNumYourTurnChats = async (userId) => {
    let user = await User.findOne({ _id: userId }).select({ 'metrics.numYourTurnChats': 1 }).lean()
    if (user && user.metrics){
      sendSocketEvent(userId, 'numYourTurnChats update', {  numYourTurnChats : user.metrics?.numYourTurnChats || 0 });
    }
}

Chat.setSendSocketUpdateForNumYourTurnChats(sendSocketUpdateForNumYourTurnChats) // injecting the function sendSocketEvent after loading the model to avoid circular dependency

async function sendAutomatedSupportReply(chat, user, booSupportUser) {
  const automatedReply = await Message.create({
    chat: chat._id,
    text: translate(AUTOMATED_SUPPORT_REPLY, user.locale || 'en'),
    sender: booSupportUser._id,
  });

  chat.lastMessage = automatedReply;
  chat.lastMessageTime = automatedReply.createdAt;
  chat.incrementNumMessages();
  incrementUnreadMessages(chat, user._id);
  chat.perUserState = [];
  chat.users.forEach((u) => {
    chat.perUserState.push({
      userId: u._id,
      unread: u._id == user._id,
    });
  });
  const savedChat = await chat.save();

  await sendSocketEvent(booSupportUser._id, 'message', automatedReply);
  await sendMessageNotifications(booSupportUser, savedChat, automatedReply, automatedReply.text);
}

const saveShadowBanUnmatchRecord = async (user) => {
  try {
    const chats = await Chat.find(
      {
        users: user._id,
        pendingUser: null,
        groupChat: { $exists: false },
        automatedChat: { $exists: false },
        deletedAt: { $exists: false },
      },
      { users: 1, lastMessageTime: 1 },
    )
      .populate({
        path: 'users',
        select: 'firstName',
      })
      .lean();

    if (!chats.length) return;

    const bulkOps = chats.map((chat) => {
      const unmatchedFor = chat.users.find((u) => u._id.toString() !== user._id.toString());
      if (!unmatchedFor) return null;

      return {
        updateOne: {
          filter: { user: unmatchedFor._id.toString(), otherUser: user._id.toString() },
          update: {
            $setOnInsert: {
              lastMessageTime: chat.lastMessageTime,
              isShadowBanUnmatch: true,
              otherUserName: user.firstName,
            },
          },
          upsert: true,
        },
      };
    }).filter(Boolean);

    const result = await Unmatched.bulkWrite(bulkOps);
    console.log(`Processed ${chats.length} chats for shadow banned user ${user._id}. Created ${result.upsertedCount} new records.`);
  } catch (error) {
    console.log(`Failed to save unmatch records for shadowbanned user ${user._id}:`, error);
  }
};

const deleteShadowBanUnmatchRecord = async (user) => {
  try {
    const { deletedCount } = await Unmatched.deleteMany({
      otherUser: user?._id?.toString(),
      isShadowBanUnmatch: true,
    });

    console.log(`Deleted ${deletedCount} shadow ban unmatch records for user ${user._id}`);
  } catch (error) {
    console.log('Failed to delete shadow ban unmatch records:', { userId: user._id, error: error.message });
  }
};

const deleteUnmatchedChatRecord = async (chat) => {
  if (!chat?.users?.length || chat.pendingUser || chat.groupChat || chat.automatedChat) return;

  const [userA, userB] = chat.users.map(u => u?._id?.toString());
  if (!userA || !userB) return;

  try {
    await Unmatched.deleteMany({
      $or: [
        { user: userA, otherUser: userB },
        { user: userB, otherUser: userA },
      ],
    });
  } catch (err) {
    console.log(`Failed to delete unmatched records for chat ${chat._id}:`, err);
  }
};

async function trackHourlyLikesReceived(user) {
  const currentHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
  const cutoff = DateTime.utc().minus({ hours: 24 });

  const map = user.metrics?.numLikesReceivedHourly;
  const entries = typeof map?.entries === 'function' ? [...map.entries()] : Object.entries(map || {});

  const unset = {};
  if (entries.length) {
    for (const [hour, c] of entries) {
      if (DateTime.fromISO(hour) < cutoff) {
        unset[`metrics.numLikesReceivedHourly.${hour}`] = '';
      }
    }
  }

  await User.updateOne(
    { _id: user._id },
    {
      $inc: { [`metrics.numLikesReceivedHourly.${currentHourKey}`]: 1 },
      ...(Object.keys(unset).length > 0 && { $unset: unset }),
    },
  );
}

function getLikesReceivedInLast24Hours(user) {
  let total = 0;
  const map = user.metrics?.numLikesReceivedHourly;
  const entries = typeof map?.entries === 'function' ? [...map.entries()] : Object.entries(map || {});
  if (!entries.length) return total;

  const cutoff = DateTime.utc().minus({ hours: 24 });
  for (const [hourKey, count] of entries) {
    const hourDate = DateTime.fromISO(hourKey);
    if (hourDate >= cutoff) {
      total += count;
    }
  }

  return total;
}

async function trackHourlyViews(user) {
  const now = DateTime.utc();
  const currentHourKey = now.startOf('hour').toISO({ suppressMilliseconds: true });
  const cutoff = now.minus({ hours: 25 });

  const unset = {};
  const inc = {
    [`metrics.numViewsReceivedHourly.${currentHourKey}`]: 1,
  };

  // Clean up old hourly views
  const hourlyViews = user.metrics?.numViewsReceivedHourly;
  const hourlyEntries =
    typeof hourlyViews?.entries === 'function'
      ? [...hourlyViews.entries()]
      : Object.entries(hourlyViews || {});

  if (hourlyEntries.length > 25) {
    for (const [hourKey] of hourlyEntries) {
      const parsed = DateTime.fromISO(hourKey);
      if (parsed < cutoff) {
        unset[`metrics.numViewsReceivedHourly.${hourKey}`] = '';
      }
    }
  }

  // Track views during active boost
  if (user.isBoostActive?.()) {
    await BoostMetric.incrementMetrics(user._id, ['numViewsReceived']);
  }

  await User.updateOne(
    { _id: user._id },
    {
      $inc: inc,
      ...(Object.keys(unset).length > 0 && { $unset: unset }),
    },
  );
}

async function calculateBoostEffectiveness(user) {
  if (user.isBoostActive?.()) return 0;

  const now = DateTime.utc();
  const cutoff = now.minus({ hours: 24 });

  // Generate 24 hour keys from cutoff to now
  const hourKeys = [];
  let hour = cutoff.startOf('hour');
  while (hour < now) {
    hourKeys.push(hour.toISO({ suppressMilliseconds: true }));
    hour = hour.plus({ hours: 1 });
  }

  // Fetch recent boosts (ending within last 24h)
  const recentBoosts = await BoostMetric.find({
    user: user._id,
    boostExpiration: { $gte: cutoff.toJSDate() },
  }).sort({ boostExpiration: -1 }).lean();
  if (recentBoosts.length === 0) return 0;

  // Track which hour keys were affected by any boost
  const excludedHourKeys = new Set();
  for (const boost of recentBoosts) {
    const expiration = DateTime.fromJSDate(boost.boostExpiration).toUTC();
    const duration = boost.durationMinutes || 60;
    const start = expiration.minus({ minutes: duration });

    let boostHour = start.startOf('hour');
    const boostEnd = expiration;

    while (boostHour < boostEnd) {
      if (boostHour >= cutoff) {
        excludedHourKeys.add(boostHour.toISO({ suppressMilliseconds: true }));
      }
      boostHour = boostHour.plus({ hours: 1 });
    }
  }

  // Prepare to sum views from non-boosted hours
  const hourlyViews = user.metrics?.numViewsReceivedHourly;
  if (!hourlyViews || hourlyViews.size === 0) return 0;

  let totalViews = 0;
  let baselineHours = 0;

  for (const hourKey of hourKeys) {
    // eslint-disable-next-line no-continue
    if (excludedHourKeys.has(hourKey)) continue;

    const views = hourlyViews.get(hourKey) || 0;
    totalViews += views;
    baselineHours += 1;
  }
  if (baselineHours === 0) return 0;

  // Use the most recent boost’s views as the "boosted" value
  const latestBoostViews = recentBoosts[0].numViewsReceived || 0;
  const avgNonBoostViews = totalViews / baselineHours;
  const boostEffectiveness = Math.floor(latestBoostViews - avgNonBoostViews);

  await BoostMetric.updateOne(
    { _id: recentBoosts[0]._id },
    { boostEffectiveness },
  );

  return boostEffectiveness;
}

module.exports = {
  formatProfile,
  formatChat,
  formatChats,
  filterChats,
  isChatVisible,
  incrementUnreadMessages,
  decrementUnreadMessages,
  clearUnreadMessages,
  BOO_SUPPORT_ID,
  getPendingChats,
  getApprovedChats,
  getSentChats,
  getContacts,
  backfillNumMessages,
  getChatExpirationDate,
  isChatExpired,
  checkForDeleteInstantMatch,
  sendSupportUserReply,
  backfillUserIdHash,
  backfillPending,
  getMessagedChats,
  getNotMessagedChats,
  backfillMessagedField,
  sendAutomatedReply,
  BOO_BOT_ID,
  backfillUserPerStateInChats,
  backfillUserNumYourTurnChats,
  decodePaginationToken,
  encodePaginationToken,
  getMessagedSortedChats,
  finalizeYourTurnStateInChats,
  sendSocketUpdateForNumYourTurnChats,
  AUTOMATED_SUPPORT_REPLY,
  sendAutomatedSupportReply,
  deleteUnmatchedChatRecord,
  saveShadowBanUnmatchRecord,
  deleteShadowBanUnmatchRecord,
  trackHourlyLikesReceived,
  getLikesReceivedInLast24Hours,
  trackHourlyViews,
  calculateBoostEffectiveness,
};
