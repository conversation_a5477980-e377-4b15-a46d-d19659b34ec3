const aws = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const cryptoRandomString = require('crypto-random-string');
const mime = require('mime-types');
const path = require('path');
const { badRequestError } = require('./http-errors');
const bunny = require('../lib/bunny');
const constants = require('../lib/constants');
const crypto = require('crypto');
const S3DeletionMetric = require('../models/s3-deletion-metric');
const S3DeleteFailure = require('../models/s3-delete-failure');

const AWS_S3_BUCKET = process.env.AWS_S3_BUCKET || 'MOCK_S3_BUCKET';

const s3 = new aws.S3({
  accessKeyId: process.env.AWS_KEY,
  secretAccessKey: process.env.AWS_SECRET,
  region: 'us-east-1',
});

const storage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = req.user._id;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

function fileFilter(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (
    !mimetype
    || !mimetype.includes('jpeg')
    && !mimetype.includes('jpg')
    && !mimetype.includes('png')
  ) {
    req.fileValidationError = badRequestError('Only jpg and png images are allowed');
    return cb(null, false);
  }
  if (
    !(fileExtension == '.jpeg')
    && !(fileExtension == '.jpg')
    && !(fileExtension == '.png')
  ) {
    req.fileValidationError = badRequestError('Only jpg and png file extensions are allowed');
    return cb(null, false);
  }
  cb(null, true);
}

function fileFilterAudio(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (
    !mimetype
    || !mimetype.includes('mpeg')
    && !mimetype.includes('aac')
    && !mimetype.includes('wav')
  ) {
    req.fileValidationError = badRequestError('Only mp3, aac and wav files are allowed');
    return cb(null, false);
  }
  if (
    !(fileExtension == '.mp3')
    && !(fileExtension == '.aac')
    && !(fileExtension == '.wav')
  ) {
    req.fileValidationError = badRequestError('Only mp3, aac and wav file extensions are allowed');
    return cb(null, false);
  }
  cb(null, true);
}

function fileFilterVideo(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (
    !mimetype
    || !mimetype.includes('video')
  ) {
    req.fileValidationError = badRequestError('Only mp4, mov and avi files are allowed');
    return cb(null, false);
  }
  if (
    !(fileExtension == '.mp4')
    && !(fileExtension == '.webm')
    && !(fileExtension == '.avi')
    && !(fileExtension == '.flv')
    && !(fileExtension == '.mkv')
    && !(fileExtension == '.mpg')
    && !(fileExtension == '.wmv')
    && !(fileExtension == '.mov')
  ) {
    req.fileValidationError = badRequestError('Only mp4, mov and avi file extensions are allowed');
    return cb(null, false);
  }
  cb(null, true);
}

function getFileType(file) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  let fileType;
  if (mimetype && (mimetype.includes('jpeg') || mimetype.includes('jpg') || mimetype.includes('png'))
      && ['.jpeg', '.jpg', '.png'].includes(fileExtension)) {
    fileType = 'image';
  }
  if (mimetype && mimetype.includes('video')
      && ['.mp4', '.webm', '.avi', '.flv', '.mkv', '.mpg', 'wmv', '.mov'].includes(fileExtension)) {
    fileType = 'video';
  }
  return fileType;
}

function fileFilterImageOrVideo(req, file, cb) {
  const mimetype = mime.lookup(file.originalname);
  const fileExtension = path.extname(file.originalname).toLowerCase();
  console.log(`Evaluating file for upload to s3: ${
    JSON.stringify(file, null, 2)
  }, file mimetype: ${mimetype
  }, file extension: ${fileExtension}`);
  if (mimetype && (mimetype.includes('jpeg') || mimetype.includes('jpg') || mimetype.includes('png'))
      && ['.jpeg', '.jpg', '.png'].includes(fileExtension)) {
    req.fileType = 'image';
    return cb(null, true);
  }
  if (mimetype && mimetype.includes('video')
      && ['.mp4', '.webm', '.avi', '.flv', '.mkv', '.mpg', 'wmv', '.mov'].includes(fileExtension)) {
    req.fileType = 'video';
    return cb(null, true);
  }

  req.fileValidationError = badRequestError('Invalid file');
  return cb(null, false);
}

const imageSizeLimit = 50 * 1000 * 1000;
const videoSizeLimit = 1000 * 1000 * 1000;
const audioSizeLimit = 15 * 1000 * 1000;

const multerUpload = () => multer({
  storage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadProfileVideo = multer({
  storage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const profileVerificationImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `${req.user._id}/verification`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadProfileVerificationImage = multer({
  storage: profileVerificationImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const livenessImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `${req.user._id}/liveness/${req.query.challengeId}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadLivenessImage = multer({
  storage: livenessImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const storyImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `${req.user._id}/stories/${req.story._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadStoryImage = multer({
  storage: storyImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadStoryImageOrVideo = multer({
  storage: storyImageStorage,
  fileFilterImageOrVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const databaseProfileImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = 'database/profiles';
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadDatabaseProfileImage = multer({
  storage: databaseProfileImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const chatImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `chats/${req.chat._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadChatImage = multer({
  storage: chatImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadChatAudio = multer({
  storage: chatImageStorage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const uploadChatVideo = multer({
  storage: chatImageStorage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const questionImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `questions/${req.question._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadQuestionImage = multer({
  storage: questionImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
    limit: 20
  },
});

const uploadQuestionVideo = multer({
  storage: questionImageStorage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const uploadQuestionAudio = multer({
  storage: questionImageStorage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const commentImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `comments/${req.comment._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadCommentImage = multer({
  storage: commentImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

const uploadCommentVideo = multer({
  storage: commentImageStorage,
  fileFilter: fileFilterVideo,
  limits: {
    fileSize: videoSizeLimit,
  },
});

const uploadCommentAudio = multer({
  storage: commentImageStorage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const uploadDescriptionAudio = multer({
  storage,
  fileFilter: fileFilterAudio,
  limits: {
    fileSize: audioSizeLimit,
  },
});

const translationImageStorage = multerS3({
  s3,
  bucket: AWS_S3_BUCKET,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key(req, file, cb) {
    const folder = `translations/${req.translation._id}`;
    const randomString = cryptoRandomString({ length: 32 });
    const fileName = `${folder}/${Date.now().toString()}${randomString}${path.extname(file.originalname)}`;
    cb(null, fileName);
  },
});

const uploadTranslationImage = multer({
  storage: translationImageStorage,
  fileFilter,
  limits: {
    fileSize: imageSizeLimit,
  },
});

async function listDirectory(dir) {
  const listParams = {
    Bucket: AWS_S3_BUCKET,
    Prefix: dir,
  };

  const listedObjects = await s3.listObjectsV2(listParams).promise();
  const keys = listedObjects.Contents.map(x => x.Key);
  return keys;
}

// Delete up to N keys (handles batching, 1 retry, stores per-key failures)
// keys: array<string>   source: string for observability   prefix: optional (for context)
async function deleteS3KeysWithRetry({ bucket, keys, source, prefix = '' }) {
  if (!keys || keys.length === 0) return { deletedCount: 0, failed: [] };

  // S3 deleteObjects max 1000 per request
  const CHUNK = 1000;
  const chunks = [];
  for (let i = 0; i < keys.length; i += CHUNK) chunks.push(keys.slice(i, i + CHUNK));

  let deletedCount = 0;
  const allFinalFailures = [];

  for (const batch of chunks) {
    const Objects = batch.map(Key => ({ Key }));
    const now = new Date();

    // 1st attempt
    let first;
    try {
      first = await s3.deleteObjects({
        Bucket: bucket,
        Delete: { Objects, Quiet: true },
      }).promise();
    } catch (e) {
      // If the request itself failed, treat the whole batch as failed; we still try the per-key retry below
      first = { Errors: Objects.map(o => ({ Key: o.Key, Code: 'RequestFailed', Message: String(e && e.message || e) })) };
    }

    const toRetry = (first.Errors || []).map(e => ({ Key: e.Key })).filter(k => k.Key);
    const initialDeleted = (first.Deleted || []).length;
    deletedCount += initialDeleted;

    let finalFailures = [];
    if (toRetry.length) {
      try {
        const retryRes = await s3.deleteObjects({
          Bucket: bucket,
          Delete: { Objects: toRetry, Quiet: true },
        }).promise();

        // count any that succeeded on retry
        deletedCount += (retryRes.Deleted || []).length;

        if (retryRes.Errors && retryRes.Errors.length) {
          finalFailures = retryRes.Errors.map(e => ({
            Key: e.Key, Code: e.Code, Message: e.Message,
          }));
        }
      } catch (retryErr) {
        finalFailures = toRetry.map(o => ({
          Key: o.Key, Code: 'RetryRequestFailed', Message: String(retryErr && retryErr.message || retryErr),
        }));
      }
    }

    if (finalFailures.length) {
      const docs = finalFailures.map(f => ({
        createdAt: now,
        bucket,
        prefix,
        key: f.Key,
        code: f.Code || null,
        message: f.Message || null,
        retryCount: 1,
      }));
      try {
        await S3DeleteFailure.insertMany(docs, { ordered: false });
      } catch (dbErr) {
        // keep the app moving, but leave a breadcrumb
        console.error('Failed to persist S3 delete failures to MongoDB', {
          bucket, prefix, source,
          failedKeys: docs.map(d => d.key),
          dbError: dbErr && dbErr.message,
        });
      }
      allFinalFailures.push(...finalFailures);
    }
  }

  return { deletedCount, failed: allFinalFailures };
}

async function emptyS3Directory(rawPrefix, source) {
  const normalize = (p) => {
    const trimmed = (p || '').replace(/^\/+|\/+$/g, '');
    return trimmed ? `${trimmed}/` : '';
  };
  const prefix = normalize(rawPrefix);

  async function emptyBucket(bucket, purgeBunny) {
    let token;
    do {
      const { Contents = [], IsTruncated, NextContinuationToken } =
        await s3.listObjectsV2({
          Bucket: bucket,
          Prefix: prefix,
          ContinuationToken: token,
          MaxKeys: 1000,
        }).promise();

      if (Contents.length === 0 && !IsTruncated) break;

      const keys = Contents.map(o => o.Key);
      if (keys.length) {
        await deleteS3KeysWithRetry({ bucket, keys, source, prefix });
      }

      token = IsTruncated ? NextContinuationToken : undefined;
    } while (token);
  }

  await emptyBucket(AWS_S3_BUCKET, true);
  const AWS_S3_BUCKET_RESIZED = constants.getS3BucketResized();
  if (AWS_S3_BUCKET_RESIZED) {
    await emptyBucket(AWS_S3_BUCKET_RESIZED, false);
  }

  await S3DeletionMetric.increment(`emptyS3Directory.${source}`, 1);
}

async function deletePicture(key, source = 'deletePicture') {
  await deleteS3KeysWithRetry({ bucket: AWS_S3_BUCKET, keys: [key], source });

  const AWS_S3_BUCKET_RESIZED = constants.getS3BucketResized();
  if (AWS_S3_BUCKET_RESIZED) {
    await deleteS3KeysWithRetry({ bucket: AWS_S3_BUCKET_RESIZED, keys: [key], source });
  }

  await emptyS3Directory(`${key}/`, source);

  await S3DeletionMetric.increment('deletePictureNumCalls', 1);
}

async function copyToBanned(key, userId) {

  const randomString = cryptoRandomString({ length: 32 });
  const newPath = `banned/${userId}/${Date.now().toString()}${randomString}${path.extname(key)}`;
  console.log(`copying ${key} to ${newPath}`);
  try {
    await s3.copyObject({
      Bucket: AWS_S3_BUCKET,
      CopySource: AWS_S3_BUCKET + '/' + key,
      Key: newPath,
    }).promise();
    return newPath;
  }
  catch (err) {
    console.log(`error AWS S3 copyObject to path ${newPath}: `, err);
  }
  return;
}

async function copyEvidenceToUserFolder(key, userId) {

  const randomString = cryptoRandomString({ length: 32 });
  const newPath = `${userId}/evidence/${Date.now().toString()}${randomString}${path.extname(key)}`;
  console.log(`copying ${key} to ${newPath}`);
  try {
    await s3.copyObject({
      Bucket: AWS_S3_BUCKET,
      CopySource: AWS_S3_BUCKET + '/' + key,
      Key: newPath,
    }).promise();
    return newPath;
  }
  catch (err) {
    console.log(`error AWS S3 copyObject to path ${newPath}: `, err);
  }
  return;
}

async function uploadUserImage(userId, data, extension) {

  const randomString = cryptoRandomString({ length: 32 });
  const path = `${userId}/${Date.now().toString()}${randomString}.${extension}`;
  try {
    await s3.putObject({
      Bucket: AWS_S3_BUCKET,
      Key: path,
      Body: data,
    }).promise();
    return path;
  }
  catch (err) {
    console.log(`error AWS S3 putObject to path ${path}: `, err);
  }
  return;
}

async function uploadYotiVerificationImage(userId, img) {
  return new Promise((resolve) => {
    try {
      const buffer = Buffer.from(img.replace(/^data:image\/\w+;base64,/, ""), 'base64');

      if (buffer.length > imageSizeLimit) {
        console.log('Image size exceeds the limit');
        return resolve(null);
      }

      const randomString = cryptoRandomString({ length: 32 });
      const fileName = `${userId}/verification/${Date.now().toString()}${randomString}.jpg`;

      const params = {
        Bucket: AWS_S3_BUCKET,
        Key: fileName,
        Body: buffer,
        ContentEncoding: 'base64',
      };

      s3.upload(params, (err, data) => {
        if (err) {
          console.log('Failed to upload Yoti verification image to S3:', err);
          resolve(null);
        } else {
          resolve(data.Key);
        }
      });
    } catch (err) {
      console.log('Failed to upload Yoti verification image to S3:', err);
      resolve(null);
    }
  });
}

async function generateHashFromS3(key) {
  try {
    const response = await s3.getObject({ Bucket: AWS_S3_BUCKET, Key: key }).promise();
    const hash = crypto.createHash('sha256').update(response.Body).digest('hex');
    return hash;
  } catch (error) {
    console.log(`Error during banned file hash generation for ${key}:`, error.message);
    return null;
  }
}

async function deleteS3ObjectsInBulk(keys, source) {
  if (!Array.isArray(keys) || keys.length === 0) {
    return;
  }

  const MAX_KEYS = 100;
  for (let i = 0; i < keys.length; i += MAX_KEYS) {
    const batch = keys.slice(i, i + MAX_KEYS);
    let promises = batch.map((key) => deletePicture(key, source));
    await Promise.all(promises);
  }
  await S3DeletionMetric.increment('deleteS3ObjectsInBulkNumKeysDeleted', keys.length);
}

module.exports = {
  s3,
  multerUpload,
  uploadProfileVideo,
  uploadProfileVerificationImage,
  uploadChatImage,
  uploadChatAudio,
  uploadChatVideo,
  uploadQuestionImage,
  uploadQuestionVideo,
  uploadQuestionAudio,
  uploadCommentImage,
  uploadCommentVideo,
  uploadCommentAudio,
  uploadDescriptionAudio,
  uploadDatabaseProfileImage,
  uploadTranslationImage,
  uploadLivenessImage,
  uploadStoryImage,
  uploadStoryImageOrVideo,
  AWS_S3_BUCKET,
  emptyS3Directory,
  deletePicture,
  copyToBanned,
  copyEvidenceToUserFolder,
  getFileType,
  listDirectory,
  uploadUserImage,
  uploadYotiVerificationImage,
  generateHashFromS3,
  deleteS3ObjectsInBulk,
};
